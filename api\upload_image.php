<?php
require_once 'config.php';
$user = requireAuth();

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    jsonResponse(['success' => false, 'message' => 'Không nhận được file ảnh'], 400);
}

$filePath = $_FILES['image']['tmp_name'];
$fileName = $_FILES['image']['name'];

$cloud_name = 'dv6hnveua';
$api_key = '216189142954892';
$api_secret = 'QumSiDqAY7-4FDWF5S9cCp8yu9P4';

$timestamp = time();
$public_id = 'chat_image_' . $timestamp . '_' . uniqid();

$params_to_sign = [
    'public_id' => $public_id,
    'timestamp' => $timestamp,
];
ksort($params_to_sign);

$signature_string = '';
foreach ($params_to_sign as $key => $value) {
    $signature_string .= $key . '=' . $value . '&';
}
$signature_string = rtrim($signature_string, '&') . $api_secret;
$signature = sha1($signature_string);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "https://api.cloudinary.com/v1_1/$cloud_name/image/upload");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, [
    'file' => new CURLFile($filePath, mime_content_type($filePath), $fileName),
    'api_key' => $api_key,
    'timestamp' => $timestamp,
    'public_id' => $public_id,
    'signature' => $signature
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code !== 200) {
    jsonResponse(['success' => false, 'message' => 'Lỗi upload lên Cloudinary', 'cloudinary_response' => $response], 500);
}

$data = json_decode($response, true);
if (!isset($data['secure_url'])) {
    jsonResponse(['success' => false, 'message' => 'Không nhận được link ảnh từ Cloudinary', 'cloudinary_response' => $response], 500);
}

jsonResponse([
    'success' => true,
    'url' => $data['secure_url']
]);
